{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "<PERSON><PERSON>(timeout 10s uvicorn:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(uvicorn:*)", "Bash(kill:*)", "Bash(lsof:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(true)", "Bash(pip install:*)", "Bash(ls:*)", "<PERSON><PERSON>(gtimeout:*)", "Bash(unset:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(cp:*)", "Bash(/opt/homebrew/bin/uvicorn app:app --host localhost --port 8082 --reload)", "<PERSON><PERSON>(chmod:*)", "Bash(./test_sync_fix.sh:*)", "<PERSON><PERSON>(jq:*)", "WebFetch(domain:docs.llamaindex.ai)", "WebFetch(domain:github.com)", "WebFetch(domain:community.llamaindex.ai)", "Bash(open http://localhost:8082)", "Bash(/dev/null)", "<PERSON><PERSON>(cat:*)", "Bash(export SMTP_PASSWORD=iarqfsiifucpzbhg)", "<PERSON><PERSON>(env)", "Bash(env:*)"], "deny": []}}