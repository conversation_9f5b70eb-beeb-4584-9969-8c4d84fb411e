import os
from pathlib import Path
from typing import Optional, List, Union, Dict, Any

try:
    from pydantic_settings import BaseSettings, SettingsConfigDict
except ImportError:
    from pydantic import BaseSettings

    # Fallback SettingsConfigDict for pydantic v1
    SettingsConfigDict = dict
from pydantic import SecretStr, Field, validator
import json
import logging
import tempfile
import base64

logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    """Application settings."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",
        case_sensitive=True,
        validate_default=False,  # Allow None values for optional fields
        use_enum_values=True,  # Use enum values instead of enum objects
    )

    # API Keys
    OPENAI_API_KEY: SecretStr = Field(..., env="OPENAI_API_KEY")
    COHERE_API_KEY: SecretStr = Field(..., env="COHERE_API_KEY")
    SESSION_SECRET_KEY: SecretStr = Field(
        default_factory=lambda: SecretStr(os.urandom(32).hex())
    )

    # Security Settings
    USERNAME: str = Field(default="admin", env="USERNAME")
    PASSWORD: SecretStr = Field(
        default_factory=lambda: SecretStr(os.urandom(16).hex()), env="PASSWORD"
    )
    ADMIN_EMAILS: str = Field(
        default="",
        env="ADMIN_EMAILS",  # Comma-separated list of admin emails
    )

    # Server Settings
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8082, env="PORT")
    CORS_ORIGINS: str = Field(default="*", env="CORS_ORIGINS")

    # SharePoint Configuration
    USE_SHAREPOINT: bool = Field(default=False, env="USE_SHAREPOINT")
    MS_CLIENT_ID: str | None = Field(default=None, env="MS_CLIENT_ID")
    MS_CLIENT_SECRET: SecretStr | None = Field(default=None, env="MS_CLIENT_SECRET")
    MS_TENANT_ID: str | None = Field(default=None, env="MS_TENANT_ID")
    SHAREPOINT_SITE_NAME: str | None = Field(default=None, env="SHAREPOINT_SITE_NAME")
    SHAREPOINT_LIBRARY_NAME: str | None = Field(
        default=None, env="SHAREPOINT_LIBRARY_NAME"
    )
    
    # Sync Configuration
    AUTO_SYNC_ENABLED: bool = Field(default=True, env="AUTO_SYNC_ENABLED")
    SYNC_INTERVAL_MINUTES: int = Field(default=15, env="SYNC_INTERVAL_MINUTES")  # Sync every 15 minutes
    DELETION_SYNC_INTERVAL_MINUTES: int = Field(default=2, env="DELETION_SYNC_INTERVAL_MINUTES")  # Fast deletion sync every 2 minutes
    SYNC_TARGET_DRIVE_ID: str | None = Field(default=None, env="SYNC_TARGET_DRIVE_ID")  # Drive to sync (leave empty for auto-discovery)
    SYNC_TARGET_FOLDER_PATH: str = Field(default="", env="SYNC_TARGET_FOLDER_PATH")  # Folder path to sync
    
    # Enhanced Automatic Sync Configuration
    AUTO_IMPORT_ENABLED: bool = Field(default=False, env="AUTO_IMPORT_ENABLED")  # Enable automatic file imports (beyond detection)
    AUTO_DELETE_ENABLED: bool = Field(default=False, env="AUTO_DELETE_ENABLED")  # Enable automatic deletion of orphaned documents
    MAX_FILES_PER_SYNC: int = Field(default=50, env="MAX_FILES_PER_SYNC")  # Maximum files to process per sync cycle
    MAX_FILE_SIZE_AUTO_IMPORT: int = Field(default=10 * 1024 * 1024, env="MAX_FILE_SIZE_AUTO_IMPORT")  # 10MB limit for auto import
    AUTO_SYNC_REQUIRE_MANUAL_APPROVAL: bool = Field(default=False, env="AUTO_SYNC_REQUIRE_MANUAL_APPROVAL")  # Require manual approval for deletions
    AUTO_SYNC_NOTIFICATION_EMAIL: str | None = Field(default=None, env="AUTO_SYNC_NOTIFICATION_EMAIL")  # Email for notifications
    AUTO_SYNC_ERROR_THRESHOLD: int = Field(default=5, env="AUTO_SYNC_ERROR_THRESHOLD")  # Max consecutive errors before disabling
    
    # Email Configuration
    SMTP_SERVER: str = Field(default="smtp.gmail.com", env="SMTP_SERVER")  # SMTP server
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")  # SMTP port (587 for TLS)
    SMTP_EMAIL: str | None = Field(default=None, env="SMTP_EMAIL")  # Sender email address
    SMTP_PASSWORD: str | None = Field(default=None, env="SMTP_PASSWORD")  # Sender email password
    EMAIL_ENABLED: bool = Field(default=False, env="EMAIL_ENABLED")  # Enable email notifications
    
    # Webhook Configuration
    WEBHOOK_NOTIFICATION_URL: str | None = Field(default=None, env="WEBHOOK_NOTIFICATION_URL")  # Webhook endpoint URL
    NOTIFICATION_WEBHOOK_URL: str | None = Field(default=None, env="NOTIFICATION_WEBHOOK_URL")  # External webhook for notifications
    ENABLE_SHAREPOINT_WEBHOOKS: bool = Field(default=True, env="ENABLE_SHAREPOINT_WEBHOOKS")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")  # development, production
    
    @property
    def effective_webhook_url(self) -> str | None:
        """Get the effective webhook URL based on environment and configuration."""
        if not self.ENABLE_SHAREPOINT_WEBHOOKS:
            return None
            
        # Use explicitly configured webhook URL if available
        if self.WEBHOOK_NOTIFICATION_URL:
            return self.WEBHOOK_NOTIFICATION_URL
            
        # For production, require HTTPS and explicit configuration
        if self.ENVIRONMENT == "production":
            # In production, webhooks must use HTTPS and be externally accessible
            return None  # Must be explicitly configured with HTTPS URL
            
        # For development, use localhost (Microsoft Graph will reject HTTP but we can test locally)
        return f"http://localhost:{self.PORT}/api/sync/webhook-notification"
    
    @property
    def webhook_supported_in_environment(self) -> bool:
        """Check if webhooks are supported in the current environment."""
        if not self.ENABLE_SHAREPOINT_WEBHOOKS:
            return False
            
        if self.ENVIRONMENT == "production":
            # Production requires explicit HTTPS webhook URL
            return bool(self.WEBHOOK_NOTIFICATION_URL and self.WEBHOOK_NOTIFICATION_URL.startswith("https://"))
            
        # Development mode - warn but allow (knowing Microsoft Graph will reject HTTP)
        return True

    # Model Settings
    OPENAI_MODEL: str = Field(default="gpt-4.1-mini", env="OPENAI_MODEL")
    CHUNK_SIZE: int = Field(default=512, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=50, env="CHUNK_OVERLAP")
    SIMILARITY_TOP_K: int = Field(default=25, env="SIMILARITY_TOP_K")
    SIMILARITY_THRESHOLD: float = Field(default=0.3, env="SIMILARITY_THRESHOLD")
    
    # LLM Parameters for comprehensive responses
    LLM_TEMPERATURE: float = Field(default=0.3, env="LLM_TEMPERATURE")  # Lower for more focused responses
    LLM_MAX_TOKENS: int = Field(default=3000, env="LLM_MAX_TOKENS")  # Increased for comprehensive responses

    # Storage Configuration (Now only local)
    STORAGE_DIR: Path = Field(default=Path("storage"))
    DATA_DIR: Path = Field(default=Path("storage/data"))  # Keep local data dir
    UPLOAD_DIR: Path = Field(
        default=Path("storage/uploads")
    )  # Keep local upload dir for temp
    TEMP_DIR: Path = Field(
        default=Path("storage/temp")
    )  # Add temp directory for SharePoint imports

    # File Handling
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=["pdf", "docx", "pptx", "txt", "md", "png", "jpg", "jpeg", "gif", "bmp"]
    )
    FILE_SIZE_LIMIT: int = Field(
        default=50 * 1024 * 1024,
        env="FILE_SIZE_LIMIT",  # 50MB limit
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        logger.info("Creating settings instance...")

        # Initialize storage backend
        if self.USE_SHAREPOINT:
            self._validate_sharepoint_config()

    def _validate_sharepoint_config(self):
        """Validate SharePoint configuration."""
        logger.info("Validating SharePoint configuration...")
        required_fields = {
            "MS_CLIENT_ID": self.MS_CLIENT_ID,
            "MS_CLIENT_SECRET": self.MS_CLIENT_SECRET,
            "MS_TENANT_ID": self.MS_TENANT_ID,
        }

        missing_fields = [
            field for field, value in required_fields.items() if not value
        ]
        if missing_fields:
            error_msg = f"Missing required SharePoint configuration: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info("SharePoint configuration validated successfully")

    @validator(
        "MS_CLIENT_ID",
        "MS_CLIENT_SECRET",
        "MS_TENANT_ID",
        "SHAREPOINT_SITE_NAME",
        "SHAREPOINT_LIBRARY_NAME",
        pre=True,
        always=True,
    )
    def check_sharepoint_fields(cls, v, values):
        if values.get("USE_SHAREPOINT") and not v:
            raise ValueError(
                f"{', '.join(values.keys())} must be set if USE_SHAREPOINT is true"
            )
        return v

    @property
    def cors_origin_list(self) -> List[str]:
        """Get list of allowed CORS origins."""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]

    def get_non_secret_config(self) -> Dict[str, Any]:
        # ... (implementation, ensure no GCS vars are included) ...
        return {
            # ... include other non-secrets ...
            "USE_SHAREPOINT": self.USE_SHAREPOINT,
            "SHAREPOINT_SITE_NAME": self.SHAREPOINT_SITE_NAME,
            "SHAREPOINT_LIBRARY_NAME": self.SHAREPOINT_LIBRARY_NAME,
            "AUTO_SYNC_ENABLED": self.AUTO_SYNC_ENABLED,
            "SYNC_INTERVAL_MINUTES": self.SYNC_INTERVAL_MINUTES,
            "DELETION_SYNC_INTERVAL_MINUTES": self.DELETION_SYNC_INTERVAL_MINUTES,
            "SYNC_TARGET_DRIVE_ID": self.SYNC_TARGET_DRIVE_ID,
            "SYNC_TARGET_FOLDER_PATH": self.SYNC_TARGET_FOLDER_PATH,
            "AUTO_IMPORT_ENABLED": self.AUTO_IMPORT_ENABLED,
            "AUTO_DELETE_ENABLED": self.AUTO_DELETE_ENABLED,
            "MAX_FILES_PER_SYNC": self.MAX_FILES_PER_SYNC,
            "MAX_FILE_SIZE_AUTO_IMPORT": self.MAX_FILE_SIZE_AUTO_IMPORT,
            "AUTO_SYNC_REQUIRE_MANUAL_APPROVAL": self.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL,
            "AUTO_SYNC_NOTIFICATION_EMAIL": self.AUTO_SYNC_NOTIFICATION_EMAIL,
            "AUTO_SYNC_ERROR_THRESHOLD": self.AUTO_SYNC_ERROR_THRESHOLD,
            "OPENAI_MODEL": self.OPENAI_MODEL,
            "CHUNK_SIZE": self.CHUNK_SIZE,
            "CHUNK_OVERLAP": self.CHUNK_OVERLAP,
            "SIMILARITY_TOP_K": self.SIMILARITY_TOP_K,
            "SIMILARITY_THRESHOLD": self.SIMILARITY_THRESHOLD,
            "LLM_TEMPERATURE": self.LLM_TEMPERATURE,
            "LLM_MAX_TOKENS": self.LLM_MAX_TOKENS,
            "STORAGE_DIR": str(self.STORAGE_DIR),
            "DATA_DIR": str(self.DATA_DIR),
            "UPLOAD_DIR": str(self.UPLOAD_DIR),
            "TEMP_DIR": str(self.TEMP_DIR),
            "ALLOWED_EXTENSIONS": self.ALLOWED_EXTENSIONS,
            "FILE_SIZE_LIMIT": self.FILE_SIZE_LIMIT,
            "CORS_ORIGINS": self.CORS_ORIGINS,
        }


# Create global settings instance
logger.info("Creating settings instance...")
settings = Settings()
logger.info(
    f"Settings configured: USE_SHAREPOINT={settings.USE_SHAREPOINT}, SHAREPOINT_SITE_NAME={settings.SHAREPOINT_SITE_NAME}"
)

# No need to create local directories when using SharePoint
