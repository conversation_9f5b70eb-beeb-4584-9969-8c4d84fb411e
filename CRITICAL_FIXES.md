# Critical Fixes Documentation

This document contains important fixes and solutions for common issues encountered in the DDBSharepoint RAG application.

## 📝 Critical Fixes to Remember

### 1. Environment Variable Conflicts

- **Problem**: Shell environment variables override .env file settings
- **Symptoms**: Mixed configurations (Gmail SMTP + Office365 credentials)
- **Solution**: Restart app with clean environment using `env -u` flags
- **Command**: 
  ```bash
  env -u AUTO_IMPORT_ENABLED -u AUTO_DELETE_ENABLED -u SMTP_SERVER -u SMTP_EMAIL -u SMTP_PASSWORD uvicorn app:app --reload
  ```

### 2. DOCX Import Failures

- **Problem**: Missing `docx2txt` package despite being in requirements.txt
- **Error**: `"docx2txt is required to read Microsoft Word files"`
- **Solution**: 
  ```bash
  pip install docx2txt
  ```

### 3. SharePoint Download Parameter Error

- **Problem**: Wrong parameter name in SharePoint client call
- **Error**: `"SharePointClient.download_file() got an unexpected keyword argument 'file_path'"`
- **Solution**: Change `file_path=` to `save_path=` in download calls
- **Code Fix**:
  ```python
  # Wrong
  await app.state.sharepoint_client.download_file(
      drive_id=drive_id,
      file_id=file_id,
      file_path=str(file_path),  # ❌ Wrong parameter
      token=token
  )
  
  # Correct
  await app.state.sharepoint_client.download_file(
      drive_id=drive_id,
      file_id=file_id,
      save_path=str(file_path),  # ✅ Correct parameter
      token=token
  )
  ```

### 4. Sync Mode Reverting on Code Changes

- **Problem**: Auto-reload resets sync mode to defaults due to environment conflicts
- **Solution**: Clean environment variables that override .env file settings
- **Key Variables**: `AUTO_IMPORT_ENABLED`, `AUTO_DELETE_ENABLED`, `AUTO_SYNC_REQUIRE_MANUAL_APPROVAL`
- **Root Cause**: Shell environment variables take precedence over .env file values

### 5. Startup Race Condition

- **Problem**: Background tasks try to access `sharepoint_client` before initialization
- **Error**: `NameError: name 'sharepoint_client' is not defined`
- **Solution**: Add proper checks for component availability before usage
- **Code Fix**:
  ```python
  # Add this check before using sharepoint_client
  if not hasattr(app.state, 'sharepoint_client') or not app.state.sharepoint_client:
      logger.info("SharePoint client not yet initialized, waiting...")
      await asyncio.sleep(30)
      continue
  ```

### 6. LlamaIndex delete_ref_doc Failure

- **Problem**: LlamaIndex's `delete_ref_doc` method was silently failing to actually remove documents from the docstore
- **Symptoms**: Background sync reports "successfully deleted" but same documents keep appearing in subsequent sync cycles, notification badges persist
- **Root Cause**: `delete_ref_doc` wasn't properly removing documents from the SimpleDocumentStore
- **Solution**: Combined multiple deletion approaches with manual docstore removal as fallback
- **Final Working Code**:
  ```python
  async def delete_document_with_verification(doc_id: str, max_retries: int = 3) -> bool:
      # Try LlamaIndex's built-in deletion first
      try:
          await asyncio.to_thread(app.state.index.delete_ref_doc, doc_id, delete_from_docstore=True)
      except Exception as e:
          logger.warning(f"LlamaIndex delete_ref_doc failed: {e}")
      
      # Manually remove from docstore if still there
      if doc_id in app.state.index.docstore.docs:
          del app.state.index.docstore.docs[doc_id]  # Direct removal
          app.state.index.docstore.delete_document(doc_id)  # LlamaIndex method
      
      # Verify deletion succeeded
      return doc_id not in app.state.index.docstore.docs
  ```

### 7. Server Timezone and Timer Issues

- **Problem**: Sync timer showing 482+ minutes instead of expected 5-minute countdown
- **Symptoms**: Frontend displays "482m 35s" countdown, sync appears to be scheduled 8+ hours in future
- **Root Cause**: Mixed timezone usage - Philippine local time (UTC+8) stored with UTC timezone labels
- **Key Issues**:
  - `get_timezone_aware_datetime()` used `datetime.now()` (local time) but labeled it as UTC
  - Background sync tasks used `datetime.now()` without timezone specification
  - `app.state.last_sync_time` assignments used local time instead of UTC
- **Solution**: Convert all sync-related timestamps to proper UTC
- **Critical Code Fixes**:
  ```python
  # Fix timezone helper function
  def get_timezone_aware_datetime(dt=None):
      """Convert datetime to timezone-aware if it's not already."""
      if dt is None:
          dt = datetime.now(timezone.utc)  # ✅ Use UTC instead of local time
      if dt.tzinfo is None:
          dt = dt.replace(tzinfo=timezone.utc)
      return dt
  
  # Fix all sync timestamp assignments
  app.state.last_sync_time = datetime.now(timezone.utc)  # ✅ Replace all instances
  sync_start_time = datetime.now(timezone.utc)  # ✅ Replace all instances
  last_error_time = datetime.now(timezone.utc)  # ✅ Replace all instances
  ```
- **Files Modified**: `/Users/<USER>/Desktop/DDBSharepoint/app.py` (Lines 144, 400, 404, 687, 1112, 1124, 1272, 1290, 1679, 1684, 6470)
- **Reset Required**: Clear `storage/data/last_sync_state.json` with current UTC time after applying fixes
- **Verification**: Server time endpoint `/api/time` should return proper UTC timestamps

## Current Working Configuration

- ✅ **Office365 SMTP**: `smtp-mail.outlook.com:587`
- ✅ **Auto-Import**: Working for .docx files
- ✅ **Environment**: Clean, respects .env file
- ✅ **Full Automation**: `AUTO_DELETE_ENABLED=true` with proper deletion logic
- ✅ **UTC Timestamps**: All sync operations use proper UTC time
- ✅ **5-Minute Timer**: Sync countdown displays correct interval

## Key Lessons Learned

### Environment Variable Precedence
The most critical lesson is **environment variable precedence** - always check for conflicting shell variables when .env settings don't take effect!

**Priority Order:**
1. Shell environment variables (highest priority)
2. .env file values
3. Default values in config.py (lowest priority)

### Troubleshooting Steps
When configuration issues occur:

1. **Check environment variables**: `env | grep -E "(AUTO_|SMTP_|EMAIL_)"`
2. **Compare with .env file**: `grep -E "(AUTO_|SMTP_|EMAIL_)" .env`
3. **Restart with clean environment** if conflicts found
4. **Verify logs** for specific error messages
5. **Check sync timestamps**: Verify `/api/time` returns UTC time
6. **Reset sync state**: Clear `storage/data/last_sync_state.json` if timer issues persist

### Auto-Import Fix Chain
The auto-import failure was actually a chain of issues:
1. **Startup race condition** → App couldn't start properly
2. **Environment conflicts** → Wrong SMTP settings loaded
3. **Missing dependency** → docx2txt not installed
4. **Parameter mismatch** → Wrong SharePoint API call

All these had to be fixed in sequence for auto-import to work correctly.

## Prevention

To prevent these issues in the future:

1. **Always use clean environment** when starting the app during development
2. **Check requirements.txt** matches actual installed packages
3. **Verify API parameter names** when making changes to SharePoint calls
4. **Test email notifications** after any configuration changes
5. **Monitor startup logs** for race conditions or missing dependencies
6. **Use UTC consistently** for all datetime operations to avoid timezone conflicts

---

*Last updated: July 5, 2025*
*These fixes ensure stable operation and prevent regression when making code changes.*