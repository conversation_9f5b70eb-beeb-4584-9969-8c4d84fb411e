# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Running the Application
- **Development**: `uvicorn app:app --host localhost --port 8082 --reload --reload-dir="." --reload-exclude="venv" --reload-exclude="storage" --reload-exclude="temp" --reload-exclude="uploads" --reload-exclude="*.log" --reload-exclude="__pycache__"`
- **Development (Alternative)**: `uvicorn app:app --host localhost --port 8082` (no auto-reload, manual restart needed)
- **Production**: `uvicorn app:app --host 0.0.0.0 --port 8082`
- Port should only be on 8082

### Testing
- **Individual test**: `python test_sharepoint_files.py`, `python test_sync_status.py`, or `python test_manual_sync.py`
- **Note**: No pytest.ini or run_tests.py found - tests are run individually

### Dependencies
- **Install**: `pip install -r requirements.txt` or `conda env create -f environment.yml`
- **Environment**: Create `.env` file with required variables (see README.md)

### Deployment Options

**Render Deployment**
- Command: `uvicorn app:app --host 0.0.0.0 --port $PORT`
- Requires `tesseract-ocr` apt package for OCR functionality
- Uses persistent disk storage at `/opt/render/project/src/storage`

**Railway Deployment**
- Uses nixpacks builder with `file` and `libmagic` packages
- Health checks on `/health` endpoint every 15s
- Auto-restart on failure with max 3 retries

**Docker Deployment**
- Build: `docker build -t ddbsharepoint .`
- Run: `docker run` with environment variables mounted
- Based on Python 3.11-slim with gcc for compilation

## Architecture Overview

This is a FastAPI-based Retrieval-Augmented Generation (RAG) application that integrates with SharePoint for document management and uses LlamaIndex + OpenAI for intelligent document querying.

### Core Components

**Main Application (`app.py`)**
- FastAPI application with comprehensive SharePoint integration
- Dual authentication: Basic Auth + Microsoft OAuth2
- RAG system using LlamaIndex, OpenAI embeddings, and Cohere reranking
- Document indexing with persistence to local storage
- Background component initialization to avoid startup delays

**SharePoint Integration (`sharepoint_client.py`)**
- Microsoft Graph API client for SharePoint operations
- Site/drive/file listing and download functionality
- Token-based authentication (tokens provided externally)
- Asynchronous operations with proper error handling

**Configuration (`config.py`)**
- Pydantic-based settings management
- Environment variable configuration
- SharePoint and storage settings validation
- Local file storage configuration (no cloud storage)

### Key Features

**Authentication System**
- Basic authentication for admin users
- Microsoft OAuth2 integration for SharePoint access
- Admin role checking via email whitelist (`ADMIN_EMAILS`)
- Session management with secure cookies

**Document Processing**
- Multi-format support: PDF, DOCX, TXT, images (with OCR), Excel files
- Automatic SharePoint document import and indexing
- Duplicate detection via SharePoint IDs
- Background processing with progress tracking

**RAG Query Engine**
- LlamaIndex vector store with OpenAI embeddings
- Cohere reranking for improved relevance
- Persistent storage with automatic index loading
- Enhanced query processing with follow-up question generation

**SharePoint Features**
- Browse SharePoint sites, drives, and folders
- Manual document import (admin-only)
- Automatic sync with SharePoint changes
- Special handling for specific organizational folders

### Data Flow

1. **Startup**: Background initialization of LLM, embeddings, and index loading
2. **Authentication**: User authenticates via Basic Auth or Microsoft OAuth
3. **Document Import**: Admin users can import from SharePoint, files are processed and indexed
4. **Querying**: Users submit queries, system retrieves relevant documents and generates responses
5. **Persistence**: Index changes are automatically persisted to local storage

### Environment Configuration

Required environment variables:
- `OPENAI_API_KEY`: OpenAI API access
- `COHERE_API_KEY`: Cohere reranking service
- `MS_CLIENT_ID`, `MS_CLIENT_SECRET`, `MS_TENANT_ID`: Microsoft Graph API access
- `USERNAME`, `PASSWORD`: Basic auth credentials
- `ADMIN_EMAILS`: Comma-separated list of admin email addresses
- `SESSION_SECRET_KEY`: Session encryption key

Optional configuration:
- `OPENAI_MODEL`: gpt-4.1-mini, gpt-4o-mini (with automatic fallback)
- `CHUNK_SIZE`: Document chunking size (default: 512)
- `CHUNK_OVERLAP`: Overlap between chunks (default: 50)
- `SIMILARITY_TOP_K`: Number of similar documents to retrieve (default: 20)
- `FILE_SIZE_LIMIT`: Maximum file upload size (default: 50MB)
- `CORS_ORIGINS`: Allowed origins for cross-origin requests

### File Organization

- `storage/`: Vector index and document storage
- `templates/`: Jinja2 HTML templates for web interface
- `static/`: Static assets (logos, icons)
- `temp/`: Temporary file processing during imports
- Test files: Various `test_*.py` files for authentication testing

### Development Notes

- The application uses async/await patterns extensively
- SharePoint operations include timeout handling and retry logic
- Admin operations are protected by role-based access control
- All document processing includes metadata preservation
- The system handles both individual files and bulk folder imports
- MSAL token caching is implemented for efficient SharePoint access

### Frontend Features

**Responsive Design**
- Desktop: Sidebar + chat interface
- Mobile: Collapsible sidebar with burger menu
- Dark mode toggle with localStorage persistence

**Real-time Features**
- Health checks every 60 seconds with auto-reconnect
- Document list refresh every 30 seconds
- Connection status monitoring

### Common Issues and Debugging

**Authentication Problems**
- Check Microsoft Graph API permissions and tenant configuration
- Verify `ADMIN_EMAILS` contains correct email addresses
- Clear browser cookies if login loops occur

**Model Initialization Issues**
- App includes automatic fallback from gpt-4.1-mini to gpt-4o-mini
- Check OpenAI API key validity and quota
- Monitor startup logs for LLM initialization errors

**SharePoint Integration Issues**
- Verify token persistence in `storage/data/token_caches/`
- Check drive permissions and site accessibility
- Monitor Graph API rate limits

### Development Guidelines

**Code Style** (from .cursor/rules/ragrules.mdc)
- Think carefully before making changes
- Use the most concise and elegant solution that changes as little code as possible
- Only action the specific task given

### Automatic Sync Configuration

**Sync Settings (from config.py)**
- `AUTO_SYNC_ENABLED`: Enable automatic sync with SharePoint (default: True)
- `SYNC_INTERVAL_MINUTES`: Regular sync interval (default: 15 minutes)
- `DELETION_SYNC_INTERVAL_MINUTES`: Fast deletion sync interval (default: 2 minutes)
- `SYNC_TARGET_DRIVE_ID`: Specific drive ID to sync (leave empty for auto-discovery)
- `SYNC_TARGET_FOLDER_PATH`: Folder path to sync (default: "")

**Enhanced Sync Features**
- `AUTO_IMPORT_ENABLED`: Enable automatic file imports beyond detection (default: False)
- `AUTO_DELETE_ENABLED`: Enable automatic deletion of orphaned documents (default: False)
- `MAX_FILES_PER_SYNC`: Maximum files to process per sync cycle (default: 50)
- `MAX_FILE_SIZE_AUTO_IMPORT`: Size limit for auto import (default: 10MB)
- `AUTO_SYNC_REQUIRE_MANUAL_APPROVAL`: Require manual approval for deletions (default: False)

**Email & Webhook Configuration**
- `SMTP_SERVER`, `SMTP_PORT`, `SMTP_EMAIL`, `SMTP_PASSWORD`: Email notification settings
- `EMAIL_ENABLED`: Enable email notifications (default: False)
- `WEBHOOK_NOTIFICATION_URL`: Webhook endpoint URL for notifications
- `ENABLE_SHAREPOINT_WEBHOOKS`: Enable SharePoint webhook integration (default: True)

### Additional Test Files

Besides the basic tests mentioned, several specialized test files exist:
- `test_deletion_fix.py`, `test_robust_deletion.py`: Test document deletion functionality
- `test_enhanced_sync.py`: Test enhanced sync features
- `test_sharepoint_notifications.py`: Test notification system
- `test_deletion_via_api.py`, `test_direct_deletion.py`: Test API-based deletion

### Background Processing

The application implements sophisticated background processing:
- Async component initialization to avoid startup delays
- Background sync with configurable intervals
- Webhook-based real-time SharePoint change notifications
- Token caching and refresh for SharePoint authentication
- Resource monitoring (with psutil when available)

### Error Handling & Monitoring

- Comprehensive logging to both console and `app.log`
- Health check endpoint at `/health` for deployment monitoring
- Automatic fallback mechanisms for OpenAI model selection
- Resource usage monitoring when psutil is available
- Token expiration handling with automatic refresh

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.